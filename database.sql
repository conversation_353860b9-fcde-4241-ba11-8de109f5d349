-- Afghan Live TV Streaming Proxy Database
-- Database: admin_ip

SET SQL_MODE = "NO_AUTO_VALUE_ON_ZERO";
START TRANSACTION;
SET time_zone = "+00:00";

-- --------------------------------------------------------

-- Table structure for table `channels`
CREATE TABLE `channels` (
  `id` bigint(20) UNSIGNED NOT NULL,
  `name` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL,
  `slug` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL,
  `playlist_url` text COLLATE utf8mb4_unicode_ci NOT NULL,
  `referer` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `logo_url` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `description` text COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `active` tinyint(1) NOT NULL DEFAULT 1,
  `sort_order` int(11) NOT NULL DEFAULT 0,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Dumping data for table `channels`
INSERT INTO `channels` (`id`, `name`, `slug`, `playlist_url`, `referer`, `logo_url`, `description`, `active`, `sort_order`, `created_at`, `updated_at`) VALUES
(1, 'Lemar TV', 'lemar', 'https://gg.hls2.xyz/live/IR%20-%20Lemar%20TV/playlist.m3u8', 'https://www.aparatchi.com/', 'https://i.imgur.com/lemar-logo.png', 'Afghan Lemar Television', 1, 1, NOW(), NOW()),
(2, 'Tolo TV', 'tolo', 'https://gg.hls2.xyz/live/AF%20-%20Tolo%20TV/playlist.m3u8', 'https://www.aparatchi.com/', 'https://i.imgur.com/tolo-logo.png', 'Tolo Television Afghanistan', 1, 2, NOW(), NOW()),
(3, '4Sports', '4sports', 'https://gg.hls2.xyz/live/4sports/playlist.m3u8', 'https://www.aparatchi.com/', 'https://i.imgur.com/4sports-logo.png', '4Sports Afghanistan', 1, 3, NOW(), NOW()),
(4, 'Ariana TV', 'ariana', 'https://gg.hls2.xyz/live/AF%20-%20Ariana%20TV/playlist.m3u8', 'https://www.aparatchi.com/', 'https://i.imgur.com/ariana-logo.png', 'Ariana Television Network', 1, 4, NOW(), NOW()),
(5, 'ATN News', 'atn', 'https://gg.hls2.xyz/live/AF%20-%20ATN%20News/playlist.m3u8', 'https://www.aparatchi.com/', 'https://i.imgur.com/atn-logo.png', 'ATN News Afghanistan', 1, 5, NOW(), NOW()),
(6, 'Shamshad TV', 'shamshad', 'https://gg.hls2.xyz/live/AF%20-%20Shamshad%20TV/playlist.m3u8', 'https://www.aparatchi.com/', 'https://i.imgur.com/shamshad-logo.png', 'Shamshad Television', 1, 6, NOW(), NOW()),
(7, 'IRIB3', 'irib3', 'https://ss.90minlive.online/live/irib3/playlist.m3u8', 'https://ss.90minlive.online/', 'https://i.imgur.com/irib3-logo.png', 'IRIB Channel 3', 1, 7, NOW(), NOW()),
(8, 'IRIB Varzesh', 'irib-varzesh', 'https://ss.90minlive.online/live/irib-varzesh/playlist.m3u8', 'https://ss.90minlive.online/', 'https://i.imgur.com/irib-varzesh-logo.png', 'IRIB Sports Channel', 1, 8, NOW(), NOW()),
(9, 'GEM FIT', 'gem-fit', 'https://ss.90minlive.online/live/gem-fit/playlist.m3u8', 'https://ss.90minlive.online/', 'https://i.imgur.com/gem-fit-logo.png', 'GEM FIT Channel', 1, 9, NOW(), NOW()),
(10, 'Persiana Fight', 'persiana-fight', 'https://gg.hls2.xyz/live/Persiana%20Fight/playlist.m3u8', 'https://www.persianleague.com/', 'https://i.imgur.com/persiana-fight-logo.png', 'Persiana Fight Channel', 1, 10, NOW(), NOW());

-- --------------------------------------------------------

-- Table structure for table `failed_jobs`
CREATE TABLE `failed_jobs` (
  `id` bigint(20) UNSIGNED NOT NULL,
  `uuid` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL,
  `connection` text COLLATE utf8mb4_unicode_ci NOT NULL,
  `queue` text COLLATE utf8mb4_unicode_ci NOT NULL,
  `payload` longtext COLLATE utf8mb4_unicode_ci NOT NULL,
  `exception` longtext COLLATE utf8mb4_unicode_ci NOT NULL,
  `failed_at` timestamp NOT NULL DEFAULT current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- --------------------------------------------------------

-- Table structure for table `migrations`
CREATE TABLE `migrations` (
  `id` int(10) UNSIGNED NOT NULL,
  `migration` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL,
  `batch` int(11) NOT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Dumping data for table `migrations`
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES
(1, '2014_10_12_000000_create_users_table', 1),
(2, '2014_10_12_100000_create_password_reset_tokens_table', 1),
(3, '2019_08_19_000000_create_failed_jobs_table', 1),
(4, '2019_12_14_000001_create_personal_access_tokens_table', 1),
(5, '2024_01_01_000000_create_channels_table', 1);

-- --------------------------------------------------------

-- Table structure for table `password_reset_tokens`
CREATE TABLE `password_reset_tokens` (
  `email` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL,
  `token` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL,
  `created_at` timestamp NULL DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- --------------------------------------------------------

-- Table structure for table `personal_access_tokens`
CREATE TABLE `personal_access_tokens` (
  `id` bigint(20) UNSIGNED NOT NULL,
  `tokenable_type` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL,
  `tokenable_id` bigint(20) UNSIGNED NOT NULL,
  `name` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL,
  `token` varchar(64) COLLATE utf8mb4_unicode_ci NOT NULL,
  `abilities` text COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `last_used_at` timestamp NULL DEFAULT NULL,
  `expires_at` timestamp NULL DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- --------------------------------------------------------

-- Table structure for table `users`
CREATE TABLE `users` (
  `id` bigint(20) UNSIGNED NOT NULL,
  `name` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL,
  `email` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL,
  `email_verified_at` timestamp NULL DEFAULT NULL,
  `password` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL,
  `remember_token` varchar(100) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Indexes for dumped tables

-- Indexes for table `channels`
ALTER TABLE `channels`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `channels_slug_unique` (`slug`),
  ADD KEY `channels_active_index` (`active`),
  ADD KEY `channels_sort_order_index` (`sort_order`);

-- Indexes for table `failed_jobs`
ALTER TABLE `failed_jobs`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `failed_jobs_uuid_unique` (`uuid`);

-- Indexes for table `migrations`
ALTER TABLE `migrations`
  ADD PRIMARY KEY (`id`);

-- Indexes for table `password_reset_tokens`
ALTER TABLE `password_reset_tokens`
  ADD PRIMARY KEY (`email`);

-- Indexes for table `personal_access_tokens`
ALTER TABLE `personal_access_tokens`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `personal_access_tokens_token_unique` (`token`),
  ADD KEY `personal_access_tokens_tokenable_type_tokenable_id_index` (`tokenable_type`,`tokenable_id`);

-- Indexes for table `users`
ALTER TABLE `users`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `users_email_unique` (`email`);

-- AUTO_INCREMENT for dumped tables

-- AUTO_INCREMENT for table `channels`
ALTER TABLE `channels`
  MODIFY `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=11;

-- AUTO_INCREMENT for table `failed_jobs`
ALTER TABLE `failed_jobs`
  MODIFY `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT;

-- AUTO_INCREMENT for table `migrations`
ALTER TABLE `migrations`
  MODIFY `id` int(10) UNSIGNED NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=6;

-- AUTO_INCREMENT for table `personal_access_tokens`
ALTER TABLE `personal_access_tokens`
  MODIFY `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT;

-- AUTO_INCREMENT for table `users`
ALTER TABLE `users`
  MODIFY `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT;

COMMIT;